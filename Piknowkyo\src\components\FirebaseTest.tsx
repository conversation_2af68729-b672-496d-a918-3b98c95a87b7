import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { db, auth, storage } from '../firebase';
import { collection, addDoc, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import storageService from '../services/storageService';

const FirebaseTest: React.FC = () => {
  const { t } = useTranslation();
  const { currentUser, userProfile, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string, isError = false) => {
    const timestamp = new Date().toLocaleTimeString();
    const formattedMessage = `[${timestamp}] ${isError ? '❌' : '✅'} ${message}`;
    setTestResults(prev => [...prev, formattedMessage]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testFirebaseConnection = async () => {
    try {
      addResult('Test de connexion Firebase...');
      
      // Test de base - vérifier que Firebase est initialisé
      if (db && auth && storage) {
        addResult('Firebase initialisé correctement');
      } else {
        addResult('Erreur d\'initialisation Firebase', true);
        return false;
      }

      return true;
    } catch (error: any) {
      addResult(`Erreur de connexion: ${error.message}`, true);
      return false;
    }
  };

  const testAuthentication = async () => {
    try {
      addResult('Test d\'authentification...');
      
      if (isAuthenticated && currentUser) {
        addResult(`Utilisateur connecté: ${currentUser.email || 'Anonyme'}`);
        addResult(`UID: ${currentUser.uid}`);
        addResult(`Type: ${currentUser.isAnonymous ? 'Anonyme' : 'Authentifié'}`);
        
        if (userProfile) {
          addResult(`Profil chargé: ${userProfile.displayName || 'Sans nom'}`);
          addResult(`Plan: ${userProfile.subscription.plan}`);
          addResult(`Langue: ${userProfile.preferences.language}`);
        } else {
          addResult('Profil utilisateur non chargé', true);
        }
        
        return true;
      } else {
        addResult('Aucun utilisateur connecté', true);
        return false;
      }
    } catch (error: any) {
      addResult(`Erreur d\'authentification: ${error.message}`, true);
      return false;
    }
  };

  const testFirestore = async () => {
    if (!isAuthenticated || !currentUser) {
      addResult('Authentification requise pour tester Firestore', true);
      return false;
    }

    try {
      addResult('Test Firestore...');
      
      // Test d'écriture
      const testData = {
        message: 'Test Firebase',
        timestamp: new Date(),
        userId: currentUser.uid
      };

      const docRef = await addDoc(collection(db, 'test_collection'), testData);
      addResult(`Document créé avec ID: ${docRef.id}`);

      // Test de lecture
      const querySnapshot = await getDocs(collection(db, 'test_collection'));
      addResult(`${querySnapshot.size} documents trouvés dans test_collection`);

      // Nettoyage - supprimer le document de test
      await deleteDoc(doc(db, 'test_collection', docRef.id));
      addResult('Document de test supprimé');

      return true;
    } catch (error: any) {
      addResult(`Erreur Firestore: ${error.message}`, true);
      return false;
    }
  };

  const testStorage = async () => {
    if (!isAuthenticated || !currentUser) {
      addResult('Authentification requise pour tester Storage', true);
      return false;
    }

    try {
      addResult('Test Firebase Storage...');
      
      // Créer un fichier de test
      const testContent = 'Test Firebase Storage';
      const testFile = new Blob([testContent], { type: 'text/plain' });
      const fileName = `test_${Date.now()}.txt`;
      const storageRef = ref(storage, `test/${currentUser.uid}/${fileName}`);

      // Upload
      await uploadBytes(storageRef, testFile);
      addResult(`Fichier uploadé: ${fileName}`);

      // Récupérer l'URL
      const downloadURL = await getDownloadURL(storageRef);
      addResult(`URL de téléchargement obtenue: ${downloadURL.substring(0, 50)}...`);

      // Supprimer le fichier de test
      await deleteObject(storageRef);
      addResult('Fichier de test supprimé');

      return true;
    } catch (error: any) {
      addResult(`Erreur Storage: ${error.message}`, true);
      return false;
    }
  };

  const testStorageService = async () => {
    if (!isAuthenticated) {
      addResult('Authentification requise pour tester StorageService', true);
      return false;
    }

    try {
      addResult('Test StorageService...');
      
      // Test de validation
      const invalidFile = new File(['test'], 'test.xyz', { type: 'application/unknown' });
      
      try {
        await storageService.uploadAudioFile(invalidFile, 'musics');
        addResult('Validation échouée - fichier invalide accepté', true);
      } catch (error: any) {
        addResult('Validation fonctionne - fichier invalide rejeté');
      }

      return true;
    } catch (error: any) {
      addResult(`Erreur StorageService: ${error.message}`, true);
      return false;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    clearResults();
    
    addResult('🚀 Début des tests Firebase...');
    
    const connectionOk = await testFirebaseConnection();
    if (!connectionOk) {
      setIsRunning(false);
      return;
    }

    await testAuthentication();
    await testFirestore();
    await testStorage();
    await testStorageService();
    
    addResult('🏁 Tests terminés');
    setIsRunning(false);
  };

  return (
    <TestContainer>
      <Title>🔥 Test d'intégration Firebase</Title>
      
      <StatusSection>
        <StatusItem>
          <strong>Statut de connexion:</strong> {isAuthenticated ? '✅ Connecté' : '❌ Non connecté'}
        </StatusItem>
        {currentUser && (
          <StatusItem>
            <strong>Utilisateur:</strong> {currentUser.email || 'Anonyme'} ({currentUser.uid.substring(0, 8)}...)
          </StatusItem>
        )}
        {userProfile && (
          <StatusItem>
            <strong>Profil:</strong> {userProfile.displayName} - {userProfile.subscription.plan}
          </StatusItem>
        )}
      </StatusSection>

      <ButtonSection>
        <TestButton onClick={runAllTests} disabled={isRunning}>
          {isRunning ? '⏳ Tests en cours...' : '🧪 Lancer tous les tests'}
        </TestButton>
        <ClearButton onClick={clearResults} disabled={isRunning}>
          🗑️ Effacer les résultats
        </ClearButton>
      </ButtonSection>

      <ResultsSection>
        <ResultsTitle>📋 Résultats des tests:</ResultsTitle>
        <ResultsList>
          {testResults.length === 0 ? (
            <EmptyMessage>Aucun test exécuté</EmptyMessage>
          ) : (
            testResults.map((result, index) => (
              <ResultItem key={index} isError={result.includes('❌')}>
                {result}
              </ResultItem>
            ))
          )}
        </ResultsList>
      </ResultsSection>

      <InfoSection>
        <InfoTitle>ℹ️ Informations</InfoTitle>
        <InfoText>
          Ce composant teste l'intégration Firebase de PiKnowKyo :
        </InfoText>
        <InfoList>
          <li>Connexion à Firebase</li>
          <li>Authentification utilisateur</li>
          <li>Lecture/écriture Firestore</li>
          <li>Upload/download Storage</li>
          <li>Services personnalisés</li>
        </InfoList>
      </InfoSection>
    </TestContainer>
  );
};

const TestContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: ${props => props.theme.surface};
  border-radius: 12px;
  box-shadow: ${props => props.theme.cardShadow};
`;

const Title = styled.h2`
  color: ${props => props.theme.text};
  margin-bottom: 1.5rem;
  text-align: center;
`;

const StatusSection = styled.div`
  background: ${props => props.theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
`;

const StatusItem = styled.div`
  color: ${props => props.theme.text};
  margin-bottom: 0.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const ButtonSection = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const TestButton = styled.button`
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${props => props.theme.primaryDark};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ClearButton = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${props => props.theme.surface};
  color: ${props => props.theme.text};
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${props => props.theme.surfaceAlt};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ResultsSection = styled.div`
  margin-bottom: 1.5rem;
`;

const ResultsTitle = styled.h3`
  color: ${props => props.theme.text};
  margin-bottom: 1rem;
`;

const ResultsList = styled.div`
  background: #1a1a1a;
  border-radius: 8px;
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
`;

const EmptyMessage = styled.div`
  color: #888;
  text-align: center;
  font-style: italic;
`;

const ResultItem = styled.div<{ isError: boolean }>`
  color: ${props => props.isError ? '#ff6b6b' : '#51cf66'};
  margin-bottom: 0.5rem;
  line-height: 1.4;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const InfoSection = styled.div`
  background: ${props => props.theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
`;

const InfoTitle = styled.h4`
  color: ${props => props.theme.text};
  margin-bottom: 0.5rem;
`;

const InfoText = styled.p`
  color: ${props => props.theme.textSecondary};
  margin-bottom: 0.5rem;
`;

const InfoList = styled.ul`
  color: ${props => props.theme.textSecondary};
  margin-left: 1rem;
  
  li {
    margin-bottom: 0.25rem;
  }
`;

export default FirebaseTest;
