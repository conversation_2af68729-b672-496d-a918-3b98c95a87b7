/**
 * Script d'initialisation pour créer le premier utilisateur administrateur
 * Usage: node scripts/init-admin.js
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';
import { config } from 'dotenv';

// Charger les variables d'environnement
config();

// Configuration Firebase
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
};

// Vérifier la configuration
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('❌ Configuration Firebase manquante');
  console.error('🔧 Vérifiez votre fichier .env');
  process.exit(1);
}

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Données du super administrateur initial
const SUPER_ADMIN = {
  email: '<EMAIL>',
  password: 'AdminPiknowkyo2024!',
  displayName: 'Super Administrateur',
  role: 'super_admin',
  permissions: [
    'manage_users',
    'manage_subscriptions',
    'manage_sessions',
    'manage_pricing',
    'view_analytics',
    'manage_content',
    'manage_ai_apis',
    'manage_acl'
  ]
};

// Plans de tarification par défaut
const DEFAULT_PRICING_PLANS = [
  {
    id: 'free',
    planName: 'Gratuit',
    price: 0,
    currency: 'EUR',
    duration: 'monthly',
    features: [
      'Accès aux sessions de base',
      'Journal personnel',
      'Statistiques basiques',
      'Support communautaire'
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'premium',
    planName: 'Premium',
    price: 9.99,
    currency: 'EUR',
    duration: 'monthly',
    features: [
      'Toutes les sessions premium',
      'Sons binauraux avancés',
      'Upload de fichiers audio',
      'Statistiques détaillées',
      'Support prioritaire',
      'Pas de publicité'
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'pro',
    planName: 'Pro',
    price: 99.99,
    currency: 'EUR',
    duration: 'yearly',
    features: [
      'Tout Premium inclus',
      'Sessions personnalisées illimitées',
      'API d\'intégration',
      'Analytics avancés',
      'Support dédié',
      'Accès anticipé aux nouvelles fonctionnalités'
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Fournisseurs IA par défaut
const DEFAULT_AI_PROVIDERS = [
  {
    id: 'groq',
    name: 'Groq',
    description: 'API Groq pour génération de contenu rapide',
    apiUrl: 'https://api.groq.com/openai/v1',
    isActive: true,
    models: ['llama3-8b-8192', 'llama3-70b-8192'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'mistral',
    name: 'Mistral AI',
    description: 'API Mistral pour génération de contenu français',
    apiUrl: 'https://api.mistral.ai/v1',
    isActive: true,
    models: ['mistral-tiny', 'mistral-small', 'mistral-medium'],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function initializeAdmin() {
  try {
    console.log('🚀 Initialisation de l\'administration PiKnowKyo...');
    console.log('====================================================');

    // 1. Créer le super administrateur
    console.log('👤 Création du super administrateur...');
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      SUPER_ADMIN.email, 
      SUPER_ADMIN.password
    );

    const adminUser = {
      email: SUPER_ADMIN.email,
      displayName: SUPER_ADMIN.displayName,
      role: SUPER_ADMIN.role,
      permissions: SUPER_ADMIN.permissions,
      createdAt: new Date(),
      isActive: true
    };

    await setDoc(doc(db, 'admin_users', userCredential.user.uid), adminUser);
    console.log('✅ Super administrateur créé avec succès');
    console.log(`📧 Email: ${SUPER_ADMIN.email}`);
    console.log(`🔑 Mot de passe: ${SUPER_ADMIN.password}`);

    // 2. Créer les plans de tarification
    console.log('\n💰 Création des plans de tarification...');
    for (const plan of DEFAULT_PRICING_PLANS) {
      await setDoc(doc(db, 'subscription_pricing', plan.id), plan);
      console.log(`✅ Plan "${plan.planName}" créé`);
    }

    // 3. Créer les fournisseurs IA
    console.log('\n🤖 Création des fournisseurs IA...');
    for (const provider of DEFAULT_AI_PROVIDERS) {
      await setDoc(doc(db, 'ai_providers', provider.id), provider);
      console.log(`✅ Fournisseur "${provider.name}" créé`);
    }

    // 4. Créer un log d'activité
    console.log('\n📝 Création du log d\'initialisation...');
    const initLog = {
      action: 'system_initialization',
      userId: userCredential.user.uid,
      details: {
        adminCreated: true,
        pricingPlansCreated: DEFAULT_PRICING_PLANS.length,
        aiProvidersCreated: DEFAULT_AI_PROVIDERS.length
      },
      timestamp: new Date(),
      ipAddress: 'localhost',
      userAgent: 'init-script'
    };

    await setDoc(doc(db, 'activity_logs', `init_${Date.now()}`), initLog);
    console.log('✅ Log d\'initialisation créé');

    console.log('\n🎉 Initialisation terminée avec succès !');
    console.log('==========================================');
    console.log('');
    console.log('📋 Prochaines étapes :');
    console.log('1. 🔐 Connectez-vous avec les identifiants admin');
    console.log('2. 🔧 Configurez les clés API dans l\'interface admin');
    console.log('3. 📊 Vérifiez les règles Firestore');
    console.log('4. 🚀 Déployez l\'application');
    console.log('');
    console.log('🔗 Console Firebase : https://console.firebase.google.com/project/piknowkyo-777');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation :', error);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('');
      console.log('ℹ️  L\'administrateur existe déjà.');
      console.log('🔧 Pour réinitialiser, supprimez l\'utilisateur dans la console Firebase Auth.');
    }
    
    process.exit(1);
  }
}

// Exécuter l'initialisation
initializeAdmin();
