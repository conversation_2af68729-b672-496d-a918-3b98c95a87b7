# 🔥 Intégration Firebase - PiKnowKyo

## ✅ Intégration Complète Terminée

L'application PiKnowKyo est maintenant entièrement intégrée avec Firebase pour l'authentification, le stockage de données et la gestion des fichiers.

## 🔧 Configuration

### Variables d'environnement
Les credentials Firebase sont maintenant sécurisées dans le fichier `.env` :

```env
REACT_APP_FIREBASE_API_KEY=AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM
REACT_APP_FIREBASE_AUTH_DOMAIN=piknowkyo-777.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=piknowkyo-777
REACT_APP_FIREBASE_STORAGE_BUCKET=piknowkyo-777.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=375619599814
REACT_APP_FIREBASE_APP_ID=1:375619599814:web:9ece9c5c2ce600a8c206c7
REACT_APP_FIREBASE_MEASUREMENT_ID=G-DSXRMZ4JP2
```

### Configuration Firebase
- **Projet** : `piknowkyo-777`
- **Persistance offline** : Activée avec la nouvelle API `localCache`
- **Émulateurs** : Support pour le développement local
- **Analytics** : Activé uniquement en production

## 🔐 Authentification

### Services d'authentification
- **Email/Mot de passe** : Inscription et connexion classique
- **Google OAuth** : Connexion avec compte Google
- **Connexion anonyme** : Pour les utilisateurs invités
- **Réinitialisation de mot de passe** : Via email

### Gestion des profils utilisateur
Chaque utilisateur a un profil complet stocké dans Firestore :
```typescript
interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  preferences: {
    language: 'fr' | 'en' | 'es';
    theme: 'light' | 'dark' | 'auto';
    notifications: boolean;
    offlineMode: boolean;
  };
  subscription: {
    plan: 'free' | 'premium' | 'pro';
    status: 'active' | 'inactive' | 'cancelled';
  };
  stats: {
    totalSessions: number;
    totalDuration: number;
    streakDays: number;
  };
}
```

## 🗄️ Structure de données Firestore

### Collections principales
- `users/{userId}` : Profils utilisateur
- `user_sessions/{sessionId}` : Sessions utilisateur
- `journal_entries/{entryId}` : Entrées de journal
- `audio_assets/{assetId}` : Assets audio personnalisés
- `scripts/{scriptId}` : Scripts publics
- `session_templates/{templateId}` : Modèles de sessions

### Collections d'administration
- `admin_users/{userId}` : Utilisateurs administrateurs
- `subscription_pricing/{planId}` : Plans de tarification
- `ai_providers/{providerId}` : Configuration IA
- `activity_logs/{logId}` : Journaux d'activité

## 🔒 Règles de sécurité Firestore

Les règles de sécurité garantissent que :
- Les utilisateurs ne peuvent accéder qu'à leurs propres données
- Les admins ont des permissions spécifiques selon leur rôle
- Le contenu public est accessible en lecture seule
- Les logs ne peuvent pas être modifiés après création

### Fonctions de sécurité principales
```javascript
function isAuthenticated() {
  return request.auth != null;
}

function isOwner(userId) {
  return isAuthenticated() && request.auth.uid == userId;
}

function isAdmin() {
  return isAuthenticated() && 
         exists(/databases/$(database)/documents/admin_users/$(request.auth.uid));
}
```

## 📁 Stockage de fichiers (Firebase Storage)

### Organisation des fichiers
```
users/
  {userId}/
    audio/
      musics/
        {timestamp}_{filename}
      ambiants/
        {timestamp}_{filename}
```

### Fonctionnalités de stockage
- **Upload avec progression** : Suivi en temps réel
- **Validation des fichiers** : Types et tailles autorisés
- **Métadonnées automatiques** : Durée, format, taille
- **Suppression sécurisée** : Nettoyage Storage + Firestore

### Types de fichiers supportés
- MP3, WAV, OGG, M4A, AAC
- Taille maximum : 50MB par fichier
- Validation côté client et serveur

## 🔄 Synchronisation offline/online

### Services de synchronisation
- `SessionSyncService` : Synchronisation des sessions
- `JournalSyncService` : Synchronisation du journal
- `AudioAssetSyncService` : Synchronisation des assets audio

### Gestion des conflits
- Détection basée sur les timestamps
- Résolution automatique ou manuelle
- Sauvegarde des versions conflictuelles

## 🎯 Composants d'interface

### Authentification
- `AuthModal` : Modal de connexion/inscription
- `UserProfile` : Profil utilisateur dans le header
- `AuthContext` : Contexte React pour l'état d'authentification

### Fonctionnalités
- Connexion automatique au démarrage
- Gestion des erreurs localisées
- Interface responsive
- Support multi-langue

## 🚀 Déploiement

### Configuration de production
1. Configurer les variables d'environnement
2. Déployer les règles Firestore : `firebase deploy --only firestore:rules`
3. Configurer l'authentification dans la console Firebase
4. Activer les services nécessaires (Storage, Analytics)

### Sécurité en production
- Variables d'environnement sécurisées
- Règles Firestore restrictives
- Validation côté serveur
- Monitoring des accès

## 📊 Monitoring et analytics

### Métriques suivies
- Connexions utilisateur
- Sessions créées/complétées
- Uploads de fichiers
- Erreurs d'authentification

### Logs d'activité
Tous les événements importants sont enregistrés dans `activity_logs` :
- Connexions/déconnexions
- Créations de contenu
- Modifications de profil
- Erreurs système

## 🔧 Maintenance

### Tâches régulières
- Nettoyage des fichiers orphelins
- Archivage des anciens logs
- Mise à jour des règles de sécurité
- Monitoring des performances

### Sauvegarde
- Firestore : Sauvegarde automatique activée
- Storage : Versioning activé
- Exports réguliers des données critiques

## 📞 Support

Pour toute question technique concernant l'intégration Firebase :
- Consulter la documentation Firebase
- Vérifier les logs dans la console Firebase
- Tester avec les émulateurs en développement

---

**Note** : Cette intégration respecte les meilleures pratiques de sécurité et de performance recommandées par Google Firebase.
