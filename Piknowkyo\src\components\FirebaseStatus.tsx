import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const FirebaseStatus: React.FC = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const checkFirebase = async () => {
      try {
        // Test simple d'import Firebase
        const { auth, db, storage } = await import('../firebase');
        
        if (auth && db && storage) {
          setStatus('success');
        } else {
          setStatus('error');
          setError('Services Firebase non initialisés');
        }
      } catch (err: any) {
        setStatus('error');
        setError(err.message || 'Erreur de chargement Firebase');
      }
    };

    checkFirebase();
  }, []);

  if (status === 'loading') {
    return (
      <StatusContainer>
        <StatusIcon>⏳</StatusIcon>
        <StatusText>Chargement Firebase...</StatusText>
      </StatusContainer>
    );
  }

  if (status === 'error') {
    return (
      <StatusContainer error>
        <StatusIcon>❌</StatusIcon>
        <StatusText>Erreur Firebase: {error}</StatusText>
      </StatusContainer>
    );
  }

  return (
    <StatusContainer success>
      <StatusIcon>✅</StatusIcon>
      <StatusText>Firebase connecté</StatusText>
    </StatusContainer>
  );
};

const StatusContainer = styled.div<{ error?: boolean; success?: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  background: ${props => 
    props.error ? '#fee' : 
    props.success ? '#efe' : 
    '#f5f5f5'
  };
  color: ${props => 
    props.error ? '#c33' : 
    props.success ? '#363' : 
    '#666'
  };
  border: 1px solid ${props => 
    props.error ? '#fcc' : 
    props.success ? '#cfc' : 
    '#ddd'
  };
`;

const StatusIcon = styled.span`
  font-size: 1rem;
`;

const StatusText = styled.span`
  font-weight: 500;
`;

export default FirebaseStatus;
