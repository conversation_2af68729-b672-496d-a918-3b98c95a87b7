import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import ReusableModal from './ReusableModal';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'signup';
}

type AuthMode = 'login' | 'signup' | 'reset';

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, initialMode = 'login' }) => {
  const { t } = useTranslation();
  const { signIn, signUp, signInWithGoogle, signInAnonymously, resetPassword } = useAuth();
  
  const [mode, setMode] = useState<AuthMode>(initialMode);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      switch (mode) {
        case 'login':
          await signIn(email, password);
          onClose();
          break;
        case 'signup':
          await signUp(email, password, displayName);
          onClose();
          break;
        case 'reset':
          await resetPassword(email);
          setMessage(t('auth.resetEmailSent'));
          break;
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError('');
    
    try {
      await signInWithGoogle();
      onClose();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAnonymousSignIn = async () => {
    setLoading(true);
    setError('');
    
    try {
      await signInAnonymously();
      onClose();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setDisplayName('');
    setError('');
    setMessage('');
  };

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode);
    resetForm();
  };

  const getTitle = () => {
    switch (mode) {
      case 'login':
        return t('auth.signIn');
      case 'signup':
        return t('auth.signUp');
      case 'reset':
        return t('auth.resetPassword');
      default:
        return t('auth.signIn');
    }
  };

  return (
    <ReusableModal
      isOpen={isOpen}
      onClose={onClose}
      title={getTitle()}
      maxWidth="400px"
    >
      <AuthContainer>
        <Form onSubmit={handleSubmit}>
          {mode === 'signup' && (
            <InputGroup>
              <Label>{t('auth.displayName')}</Label>
              <Input
                type="text"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder={t('auth.displayNamePlaceholder')}
              />
            </InputGroup>
          )}

          <InputGroup>
            <Label>{t('auth.email')}</Label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={t('auth.emailPlaceholder')}
              required
            />
          </InputGroup>

          {mode !== 'reset' && (
            <InputGroup>
              <Label>{t('auth.password')}</Label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={t('auth.passwordPlaceholder')}
                required
                minLength={6}
              />
            </InputGroup>
          )}

          {error && <ErrorMessage>{error}</ErrorMessage>}
          {message && <SuccessMessage>{message}</SuccessMessage>}

          <SubmitButton type="submit" disabled={loading}>
            {loading ? t('common.loading') : getTitle()}
          </SubmitButton>
        </Form>

        {mode !== 'reset' && (
          <>
            <Divider>
              <span>{t('auth.or')}</span>
            </Divider>

            <SocialButtons>
              <GoogleButton onClick={handleGoogleSignIn} disabled={loading}>
                <GoogleIcon>🔍</GoogleIcon>
                {t('auth.signInWithGoogle')}
              </GoogleButton>

              <AnonymousButton onClick={handleAnonymousSignIn} disabled={loading}>
                👤 {t('auth.continueAsGuest')}
              </AnonymousButton>
            </SocialButtons>
          </>
        )}

        <AuthLinks>
          {mode === 'login' && (
            <>
              <LinkButton onClick={() => switchMode('signup')}>
                {t('auth.noAccount')}
              </LinkButton>
              <LinkButton onClick={() => switchMode('reset')}>
                {t('auth.forgotPassword')}
              </LinkButton>
            </>
          )}

          {mode === 'signup' && (
            <LinkButton onClick={() => switchMode('login')}>
              {t('auth.hasAccount')}
            </LinkButton>
          )}

          {mode === 'reset' && (
            <LinkButton onClick={() => switchMode('login')}>
              {t('auth.backToSignIn')}
            </LinkButton>
          )}
        </AuthLinks>
      </AuthContainer>
    </ReusableModal>
  );
};

const AuthContainer = styled.div`
  padding: 1rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: ${props => props.theme.text};
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  font-size: 1rem;
  background: ${props => props.theme.surface};
  color: ${props => props.theme.text};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.primary}20;
  }
`;

const SubmitButton = styled.button`
  padding: 0.75rem;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${props => props.theme.primaryDark};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: ${props => props.theme.border};
  }

  span {
    padding: 0 1rem;
    color: ${props => props.theme.textSecondary};
    font-size: 0.9rem;
  }
`;

const SocialButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const GoogleButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f5f5f5;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const GoogleIcon = styled.span`
  font-size: 1.2rem;
`;

const AnonymousButton = styled.button`
  padding: 0.75rem;
  background: ${props => props.theme.surface};
  color: ${props => props.theme.text};
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${props => props.theme.surfaceAlt};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const AuthLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1.5rem;
  align-items: center;
`;

const LinkButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.primary};
  font-size: 0.9rem;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: ${props => props.theme.primaryDark};
  }
`;

const ErrorMessage = styled.div`
  color: #e74c3c;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: #e74c3c20;
  border-radius: 4px;
`;

const SuccessMessage = styled.div`
  color: #27ae60;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: #27ae6020;
  border-radius: 4px;
`;

export default AuthModal;
