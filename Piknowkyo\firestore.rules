rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Fonction pour vérifier si l'utilisateur est authentifié
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Fonction pour vérifier si l'utilisateur est propriétaire du document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Fonction pour vérifier si l'utilisateur est admin
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Fonction pour vérifier les permissions admin
    function hasPermission(permission) {
      return isAdmin() && 
             permission in get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.permissions;
    }
    
    // Fonction pour vérifier si l'utilisateur est super admin
    function isSuperAdmin() {
      return isAdmin() && 
             get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.role == 'super_admin';
    }

    // === RÈGLES POUR LES UTILISATEURS NORMAUX ===
    
    // Profils utilisateurs
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin(); // Les admins peuvent lire tous les profils
      allow write: if hasPermission('manage_users'); // Seuls les admins avec permission peuvent modifier
    }
    
    // Sessions utilisateur
    match /user_sessions/{sessionId} {
      allow read, write: if isAuthenticated() && 
                           resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                      request.resource.data.userId == request.auth.uid;
      allow read: if isAdmin() && hasPermission('view_analytics');
    }
    
    // Entrées de journal personnel
    match /journal_entries/{entryId} {
      allow read, write: if isAuthenticated() && 
                           resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                      request.resource.data.userId == request.auth.uid;
    }
    
    // Assets audio personnels
    match /audio_assets/{assetId} {
      allow read, write: if isAuthenticated() && 
                           resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                      request.resource.data.userId == request.auth.uid;
    }
    
    // Scripts personnalisés par utilisateur
    match /user_scripts/{scriptId} {
      allow read, write: if isAuthenticated() && 
                           resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                      request.resource.data.userId == request.auth.uid;
    }
    
    // Préférences utilisateur
    match /user_preferences/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // === RÈGLES POUR LE CONTENU PUBLIC ===
    
    // Scripts publics (lecture seule pour tous, écriture pour les admins)
    match /scripts/{scriptId} {
      allow read: if true; // Lecture publique
      allow write: if hasPermission('manage_content');
    }
    
    // Modèles de sessions (lecture seule pour tous, écriture pour les admins)
    match /session_templates/{templateId} {
      allow read: if true; // Lecture publique
      allow write: if hasPermission('manage_sessions') || hasPermission('manage_content');
    }
    
    // Catégories (lecture seule pour tous, écriture pour les admins)
    match /categories/{categoryId} {
      allow read: if true; // Lecture publique
      allow write: if hasPermission('manage_content');
    }
    
    // === RÈGLES POUR L'ADMINISTRATION ===
    
    // Utilisateurs administrateurs
    match /admin_users/{userId} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin() || hasPermission('manage_acl');
    }
    
    // Plans de tarification
    match /subscription_pricing/{planId} {
      allow read: if isAuthenticated(); // Les utilisateurs peuvent voir les plans
      allow write: if hasPermission('manage_pricing');
    }
    
    // Abonnements utilisateurs
    match /user_subscriptions/{subscriptionId} {
      allow read: if isAuthenticated() && 
                    resource.data.userId == request.auth.uid;
      allow write: if hasPermission('manage_subscriptions');
    }
    
    // Fournisseurs IA
    match /ai_providers/{providerId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_ai_apis');
    }
    
    // Journaux d'activité
    match /activity_logs/{logId} {
      allow read: if isAdmin() && hasPermission('view_analytics');
      allow create: if isAuthenticated(); // Tous les utilisateurs peuvent créer des logs
      allow update, delete: if false; // Les logs ne peuvent pas être modifiés
    }
    
    // === RÈGLES POUR LES STATISTIQUES ===
    
    // Statistiques globales (lecture seule pour les admins)
    match /global_stats/{statId} {
      allow read: if isAdmin() && hasPermission('view_analytics');
      allow write: if false; // Géré par des fonctions cloud
    }
    
    // Statistiques utilisateur
    match /user_stats/{userId} {
      allow read: if isOwner(userId);
      allow read: if isAdmin() && hasPermission('view_analytics');
      allow write: if false; // Géré par des fonctions cloud
    }
    
    // === RÈGLES POUR LA SYNCHRONISATION ===
    
    // Queue de synchronisation (pour le mode offline)
    match /sync_queue/{userId} {
      match /pending_changes/{changeId} {
        allow read, write: if isOwner(userId);
      }
    }
    
    // === RÈGLES PAR DÉFAUT ===
    
    // Refuser tout accès non autorisé
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
