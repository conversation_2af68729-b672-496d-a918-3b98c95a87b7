import { db, auth } from '../firebase';
import {
  collection,
  getDocs,
  setDoc,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  Timestamp,
} from 'firebase/firestore';
import { Session, JournalEntry, AudioAsset } from '../models';

// Types pour la synchronisation
export interface SyncResult {
  success: boolean;
  syncedCount: number;
  conflicts: Array<{
    id: string;
    type: string;
    localData: any;
    remoteData: any;
  }>;
  errors: string[];
}

export interface PendingChanges {
  created: any[];
  updated: any[];
  deleted: string[];
}

// Service de synchronisation pour les sessions
export class SessionSyncService {
  private static readonly COLLECTION = 'sessions';

  static async syncToFirebase(pendingChanges: PendingChanges): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      conflicts: [],
      errors: [],
    };

    if (!auth.currentUser) {
      result.success = false;
      result.errors.push('User not authenticated');
      return result;
    }

    const batch = writeBatch(db);
    const userId = auth.currentUser.uid;

    try {
      // Créer les nouvelles sessions
      for (const session of pendingChanges.created) {
        const sessionDoc = doc(db, this.COLLECTION, session.id);
        batch.set(sessionDoc, {
          ...session,
          userId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
        result.syncedCount++;
      }

      // Mettre à jour les sessions existantes
      for (const session of pendingChanges.updated) {
        const sessionDoc = doc(db, this.COLLECTION, session.id);
        batch.update(sessionDoc, {
          ...session,
          updatedAt: serverTimestamp(),
        });
        result.syncedCount++;
      }

      // Supprimer les sessions
      for (const sessionId of pendingChanges.deleted) {
        const sessionDoc = doc(db, this.COLLECTION, sessionId);
        batch.delete(sessionDoc);
        result.syncedCount++;
      }

      await batch.commit();
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message);
    }

    return result;
  }

  static async fetchFromFirebase(): Promise<Session[]> {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Session[];
  }

  static subscribeToChanges(callback: (sessions: Session[]) => void) {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const sessions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as Session[];
      callback(sessions);
    });
  }
}

// Service de synchronisation pour le journal
export class JournalSyncService {
  private static readonly COLLECTION = 'journal_entries';

  static async syncToFirebase(pendingChanges: PendingChanges): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      conflicts: [],
      errors: [],
    };

    if (!auth.currentUser) {
      result.success = false;
      result.errors.push('User not authenticated');
      return result;
    }

    const batch = writeBatch(db);
    const userId = auth.currentUser.uid;

    try {
      // Créer les nouvelles entrées
      for (const entry of pendingChanges.created) {
        const entryDoc = doc(db, this.COLLECTION, entry.id);
        batch.set(entryDoc, {
          ...entry,
          userId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
        result.syncedCount++;
      }

      // Mettre à jour les entrées existantes
      for (const entry of pendingChanges.updated) {
        const entryDoc = doc(db, this.COLLECTION, entry.id);
        batch.update(entryDoc, {
          ...entry,
          updatedAt: serverTimestamp(),
        });
        result.syncedCount++;
      }

      // Supprimer les entrées
      for (const entryId of pendingChanges.deleted) {
        const entryDoc = doc(db, this.COLLECTION, entryId);
        batch.delete(entryDoc);
        result.syncedCount++;
      }

      await batch.commit();
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message);
    }

    return result;
  }

  static async fetchFromFirebase(): Promise<JournalEntry[]> {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('date', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as JournalEntry[];
  }

  static subscribeToChanges(callback: (entries: JournalEntry[]) => void) {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('date', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const entries = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as JournalEntry[];
      callback(entries);
    });
  }
}

// Service de synchronisation pour les assets audio
export class AudioAssetSyncService {
  private static readonly COLLECTION = 'audio_assets';

  static async syncToFirebase(pendingChanges: PendingChanges): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      conflicts: [],
      errors: [],
    };

    if (!auth.currentUser) {
      result.success = false;
      result.errors.push('User not authenticated');
      return result;
    }

    const batch = writeBatch(db);
    const userId = auth.currentUser.uid;

    try {
      // Créer les nouveaux assets
      for (const asset of pendingChanges.created) {
        if (asset.isUserUploaded) {
          const assetDoc = doc(db, this.COLLECTION, asset.id);
          batch.set(assetDoc, {
            ...asset,
            userId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });
          result.syncedCount++;
        }
      }

      // Mettre à jour les assets existants
      for (const asset of pendingChanges.updated) {
        if (asset.isUserUploaded) {
          const assetDoc = doc(db, this.COLLECTION, asset.id);
          batch.update(assetDoc, {
            ...asset,
            updatedAt: serverTimestamp(),
          });
          result.syncedCount++;
        }
      }

      // Supprimer les assets
      for (const assetId of pendingChanges.deleted) {
        const assetDoc = doc(db, this.COLLECTION, assetId);
        batch.delete(assetDoc);
        result.syncedCount++;
      }

      await batch.commit();
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message);
    }

    return result;
  }

  static async fetchFromFirebase(): Promise<AudioAsset[]> {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as AudioAsset[];
  }

  static subscribeToChanges(callback: (assets: AudioAsset[]) => void) {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    const userId = auth.currentUser.uid;
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const assets = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as AudioAsset[];
      callback(assets);
    });
  }
}

// Service principal de synchronisation
export class SyncService {
  static async performFullSync(): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      conflicts: [],
      errors: [],
    };

    try {
      // TODO: Implémenter la synchronisation complète
      // 1. Récupérer les données depuis Firebase
      // 2. Comparer avec les données locales
      // 3. Résoudre les conflits
      // 4. Synchroniser les changements

      console.log('Full sync not implemented yet');
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message);
    }

    return result;
  }

  static detectConflicts(localData: any, remoteData: any): boolean {
    // Logique simple de détection de conflit basée sur les timestamps
    if (!localData.updatedAt || !remoteData.updatedAt) {
      return false;
    }

    const localTime = localData.updatedAt instanceof Timestamp
      ? localData.updatedAt.toMillis()
      : new Date(localData.updatedAt).getTime();

    const remoteTime = remoteData.updatedAt instanceof Timestamp
      ? remoteData.updatedAt.toMillis()
      : new Date(remoteData.updatedAt).getTime();

    // Conflit si les deux ont été modifiés dans les 5 dernières minutes
    const timeDiff = Math.abs(localTime - remoteTime);
    return timeDiff < 5 * 60 * 1000; // 5 minutes
  }
}
