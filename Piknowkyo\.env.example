# Configuration Firebase - PiKnowKyo
# Copiez ce fichier vers .env et remplissez avec vos vraies valeurs

# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain_here
REACT_APP_FIREBASE_PROJECT_ID=your_project_id_here
REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket_here
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
REACT_APP_FIREBASE_APP_ID=your_app_id_here
REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# Development Settings
REACT_APP_USE_FIREBASE_EMULATOR=false
NODE_ENV=development

# Optional: AI API Keys (can be managed via admin panel)
REACT_APP_GROQ_API_KEY=your_groq_api_key_here
REACT_APP_MISTRAL_API_KEY=your_mistral_api_key_here
REACT_APP_GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# App Settings
REACT_APP_APP_VERSION=1.0.0
REACT_APP_DEBUG_MODE=false
