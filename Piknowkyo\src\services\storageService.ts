import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject, 
  uploadBytesResumable,
  UploadTaskSnapshot 
} from 'firebase/storage';
import { doc, setDoc, deleteDoc, updateDoc } from 'firebase/firestore';
import { storage, db, auth } from '../firebase';
import { AudioAsset } from '../models';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  downloadURL?: string;
  audioAsset?: AudioAsset;
  error?: string;
}

class StorageService {
  
  // Upload d'un fichier audio avec progression
  async uploadAudioFile(
    file: File, 
    type: 'musics' | 'ambiants',
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    if (!auth.currentUser) {
      return { success: false, error: 'Utilisateur non connecté' };
    }

    try {
      // Validation du fichier
      const validationError = this.validateAudioFile(file);
      if (validationError) {
        return { success: false, error: validationError };
      }

      const userId = auth.currentUser.uid;
      const fileName = `${Date.now()}_${file.name}`;
      const filePath = `users/${userId}/audio/${type}/${fileName}`;
      
      // Créer la référence de stockage
      const storageRef = ref(storage, filePath);
      
      // Upload avec progression
      const uploadTask = uploadBytesResumable(storageRef, file);
      
      return new Promise((resolve) => {
        uploadTask.on(
          'state_changed',
          (snapshot: UploadTaskSnapshot) => {
            // Progression
            const progress: UploadProgress = {
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
              percentage: (snapshot.bytesTransferred / snapshot.totalBytes) * 100
            };
            
            if (onProgress) {
              onProgress(progress);
            }
          },
          (error) => {
            // Erreur
            console.error('Erreur upload:', error);
            resolve({ success: false, error: 'Erreur lors de l\'upload' });
          },
          async () => {
            // Succès
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              
              // Créer l'asset audio dans Firestore
              const audioAsset = await this.createAudioAssetRecord(
                file, 
                downloadURL, 
                type, 
                filePath
              );
              
              resolve({ 
                success: true, 
                downloadURL, 
                audioAsset 
              });
            } catch (error) {
              console.error('Erreur lors de la création de l\'enregistrement:', error);
              resolve({ success: false, error: 'Erreur lors de la sauvegarde' });
            }
          }
        );
      });
      
    } catch (error: any) {
      console.error('Erreur upload:', error);
      return { success: false, error: error.message };
    }
  }

  // Supprimer un fichier audio
  async deleteAudioFile(audioAsset: AudioAsset): Promise<boolean> {
    if (!auth.currentUser) {
      throw new Error('Utilisateur non connecté');
    }

    try {
      // Supprimer le fichier du Storage
      if (audioAsset.storagePath) {
        const storageRef = ref(storage, audioAsset.storagePath);
        await deleteObject(storageRef);
      }

      // Supprimer l'enregistrement de Firestore
      await deleteDoc(doc(db, 'audio_assets', audioAsset.id));

      return true;
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      throw new Error('Erreur lors de la suppression du fichier');
    }
  }

  // Créer l'enregistrement dans Firestore
  private async createAudioAssetRecord(
    file: File,
    downloadURL: string,
    type: 'musics' | 'ambiants',
    storagePath: string
  ): Promise<AudioAsset> {
    if (!auth.currentUser) {
      throw new Error('Utilisateur non connecté');
    }

    const audioAsset: AudioAsset = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: file.name.replace(/\.[^/.]+$/, ''), // Nom sans extension
      url: downloadURL,
      type: type,
      duration: 0, // À déterminer côté client
      size: file.size,
      format: file.type,
      isUserUploaded: true,
      userId: auth.currentUser.uid,
      storagePath: storagePath,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Sauvegarder dans Firestore
    await setDoc(doc(db, 'audio_assets', audioAsset.id), audioAsset);

    return audioAsset;
  }

  // Mettre à jour les métadonnées d'un asset (ex: durée)
  async updateAudioAssetMetadata(
    assetId: string, 
    metadata: Partial<AudioAsset>
  ): Promise<void> {
    if (!auth.currentUser) {
      throw new Error('Utilisateur non connecté');
    }

    try {
      await updateDoc(doc(db, 'audio_assets', assetId), {
        ...metadata,
        updatedAt: new Date(),
      });
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour des métadonnées:', error);
      throw new Error('Erreur lors de la mise à jour');
    }
  }

  // Validation du fichier audio
  private validateAudioFile(file: File): string | null {
    // Vérifier le type de fichier
    const allowedTypes = [
      'audio/mpeg',
      'audio/mp3', 
      'audio/wav',
      'audio/ogg',
      'audio/m4a',
      'audio/aac'
    ];

    if (!allowedTypes.includes(file.type)) {
      return 'Format de fichier non supporté. Utilisez MP3, WAV, OGG, M4A ou AAC.';
    }

    // Vérifier la taille (max 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return 'Le fichier est trop volumineux. Taille maximum: 50MB.';
    }

    // Vérifier le nom du fichier
    if (file.name.length > 100) {
      return 'Le nom du fichier est trop long (max 100 caractères).';
    }

    return null;
  }

  // Obtenir l'URL de téléchargement d'un fichier
  async getDownloadURL(storagePath: string): Promise<string> {
    try {
      const storageRef = ref(storage, storagePath);
      return await getDownloadURL(storageRef);
    } catch (error: any) {
      console.error('Erreur lors de la récupération de l\'URL:', error);
      throw new Error('Impossible de récupérer le fichier');
    }
  }

  // Calculer l'espace de stockage utilisé par l'utilisateur
  async getUserStorageUsage(): Promise<{ totalSize: number; fileCount: number }> {
    // Cette fonction nécessiterait une fonction cloud pour être efficace
    // Pour l'instant, on retourne des valeurs par défaut
    return { totalSize: 0, fileCount: 0 };
  }

  // Nettoyer les fichiers orphelins (sans enregistrement Firestore)
  async cleanupOrphanedFiles(): Promise<void> {
    // Cette fonction nécessiterait une fonction cloud pour être sécurisée
    console.log('Cleanup des fichiers orphelins - à implémenter côté serveur');
  }
}

// Instance singleton
export const storageService = new StorageService();
export default storageService;
