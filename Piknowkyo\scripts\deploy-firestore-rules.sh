#!/bin/bash

# Script de déploiement des règles Firestore pour PiKnowKyo
# Usage: ./scripts/deploy-firestore-rules.sh

echo "🔥 Déploiement des règles Firestore pour PiKnowKyo"
echo "=================================================="

# Vérifier si Firebase CLI est installé
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI n'est pas installé."
    echo "📦 Installation avec npm :"
    echo "   npm install -g firebase-tools"
    echo ""
    echo "📦 Ou avec curl :"
    echo "   curl -sL https://firebase.tools | bash"
    exit 1
fi

echo "✅ Firebase CLI détecté"

# Vérifier si l'utilisateur est connecté
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Connexion à Firebase requise..."
    firebase login
fi

echo "✅ Utilisateur connecté à Firebase"

# Vérifier le projet actuel
CURRENT_PROJECT=$(firebase use --json | jq -r '.result.project // empty')

if [ "$CURRENT_PROJECT" != "piknowkyo-777" ]; then
    echo "🔄 Configuration du projet piknowkyo-777..."
    firebase use piknowkyo-777
    
    if [ $? -ne 0 ]; then
        echo "❌ Impossible de sélectionner le projet piknowkyo-777"
        echo "🔧 Vérifiez que vous avez accès au projet"
        exit 1
    fi
fi

echo "✅ Projet piknowkyo-777 sélectionné"

# Vérifier que le fichier de règles existe
if [ ! -f "firestore.rules" ]; then
    echo "❌ Fichier firestore.rules introuvable"
    echo "📁 Assurez-vous d'être dans le répertoire racine du projet"
    exit 1
fi

echo "✅ Fichier firestore.rules trouvé"

# Afficher un aperçu des règles
echo ""
echo "📋 Aperçu des règles à déployer :"
echo "================================"
head -20 firestore.rules
echo "..."
echo ""

# Demander confirmation
read -p "🤔 Voulez-vous déployer ces règles ? (y/N) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Déploiement annulé"
    exit 0
fi

echo "🚀 Déploiement des règles Firestore..."

# Déployer les règles
firebase deploy --only firestore:rules

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Règles Firestore déployées avec succès !"
    echo "🔗 Console Firebase : https://console.firebase.google.com/project/piknowkyo-777/firestore/rules"
    echo ""
    echo "📊 Vérifications recommandées :"
    echo "  1. Tester l'authentification des utilisateurs"
    echo "  2. Vérifier les permissions de lecture/écriture"
    echo "  3. Tester les règles d'administration"
    echo "  4. Contrôler les logs de sécurité"
else
    echo ""
    echo "❌ Erreur lors du déploiement des règles"
    echo "🔧 Vérifiez :"
    echo "  1. La syntaxe du fichier firestore.rules"
    echo "  2. Vos permissions sur le projet Firebase"
    echo "  3. La connexion internet"
    exit 1
fi
