import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged, 
  User,
  updateProfile,
  sendPasswordResetEmail,
  GoogleAuthProvider,
  signInWithPopup,
  signInAnonymously
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { auth, db } from '../firebase';

export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  isAnonymous: boolean;
  createdAt: any;
  lastLoginAt: any;
  preferences: {
    language: 'fr' | 'en' | 'es';
    theme: 'light' | 'dark' | 'auto';
    notifications: boolean;
    offlineMode: boolean;
  };
  subscription: {
    plan: 'free' | 'premium' | 'pro';
    status: 'active' | 'inactive' | 'cancelled';
    expiresAt: any;
  };
  stats: {
    totalSessions: number;
    totalDuration: number;
    streakDays: number;
    lastSessionAt: any;
  };
}

class AuthService {
  private currentUser: User | null = null;
  private userProfile: UserProfile | null = null;

  constructor() {
    // Écouter les changements d'état d'authentification
    onAuthStateChanged(auth, async (user) => {
      this.currentUser = user;
      if (user) {
        await this.loadUserProfile(user.uid);
        await this.updateLastLogin(user.uid);
      } else {
        this.userProfile = null;
      }
    });
  }

  // Inscription avec email/mot de passe
  async signUpWithEmail(email: string, password: string, displayName?: string): Promise<UserProfile> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Mettre à jour le profil avec le nom d'affichage
      if (displayName) {
        await updateProfile(user, { displayName });
      }

      // Créer le profil utilisateur dans Firestore
      const userProfile = await this.createUserProfile(user, displayName);
      
      return userProfile;
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Connexion avec email/mot de passe
  async signInWithEmail(email: string, password: string): Promise<UserProfile> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const userProfile = await this.loadUserProfile(userCredential.user.uid);
      
      if (!userProfile) {
        throw new Error('Profil utilisateur introuvable');
      }
      
      return userProfile;
    } catch (error: any) {
      console.error('Erreur lors de la connexion:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Connexion avec Google
  async signInWithGoogle(): Promise<UserProfile> {
    try {
      const provider = new GoogleAuthProvider();
      const userCredential = await signInWithPopup(auth, provider);
      const user = userCredential.user;

      // Vérifier si c'est un nouvel utilisateur
      let userProfile = await this.loadUserProfile(user.uid);
      
      if (!userProfile) {
        // Créer le profil pour un nouvel utilisateur Google
        userProfile = await this.createUserProfile(user);
      }
      
      return userProfile;
    } catch (error: any) {
      console.error('Erreur lors de la connexion Google:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Connexion anonyme
  async signInAnonymously(): Promise<UserProfile> {
    try {
      const userCredential = await signInAnonymously(auth);
      const user = userCredential.user;
      
      // Créer un profil temporaire pour l'utilisateur anonyme
      const userProfile = await this.createUserProfile(user, 'Utilisateur Anonyme');
      
      return userProfile;
    } catch (error: any) {
      console.error('Erreur lors de la connexion anonyme:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Déconnexion
  async signOut(): Promise<void> {
    try {
      await signOut(auth);
      this.currentUser = null;
      this.userProfile = null;
    } catch (error: any) {
      console.error('Erreur lors de la déconnexion:', error);
      throw new Error('Erreur lors de la déconnexion');
    }
  }

  // Réinitialisation du mot de passe
  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Erreur lors de la réinitialisation:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Créer un profil utilisateur dans Firestore
  private async createUserProfile(user: User, displayName?: string): Promise<UserProfile> {
    const userProfile: UserProfile = {
      uid: user.uid,
      email: user.email,
      displayName: displayName || user.displayName,
      photoURL: user.photoURL,
      isAnonymous: user.isAnonymous,
      createdAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
      preferences: {
        language: 'fr',
        theme: 'auto',
        notifications: true,
        offlineMode: true,
      },
      subscription: {
        plan: 'free',
        status: 'active',
        expiresAt: null,
      },
      stats: {
        totalSessions: 0,
        totalDuration: 0,
        streakDays: 0,
        lastSessionAt: null,
      },
    };

    await setDoc(doc(db, 'users', user.uid), userProfile);
    this.userProfile = userProfile;
    
    return userProfile;
  }

  // Charger le profil utilisateur depuis Firestore
  private async loadUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      
      if (userDoc.exists()) {
        const data = userDoc.data() as UserProfile;
        this.userProfile = data;
        return data;
      }
      
      return null;
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error);
      return null;
    }
  }

  // Mettre à jour la dernière connexion
  private async updateLastLogin(uid: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        lastLoginAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la dernière connexion:', error);
    }
  }

  // Mettre à jour le profil utilisateur
  async updateUserProfile(updates: Partial<UserProfile>): Promise<void> {
    if (!this.currentUser) {
      throw new Error('Utilisateur non connecté');
    }

    try {
      await updateDoc(doc(db, 'users', this.currentUser.uid), updates);
      
      // Mettre à jour le profil local
      if (this.userProfile) {
        this.userProfile = { ...this.userProfile, ...updates };
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      throw new Error('Erreur lors de la mise à jour du profil');
    }
  }

  // Getters
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  getUserProfile(): UserProfile | null {
    return this.userProfile;
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  // Messages d'erreur localisés
  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'Aucun utilisateur trouvé avec cette adresse email';
      case 'auth/wrong-password':
        return 'Mot de passe incorrect';
      case 'auth/email-already-in-use':
        return 'Cette adresse email est déjà utilisée';
      case 'auth/weak-password':
        return 'Le mot de passe doit contenir au moins 6 caractères';
      case 'auth/invalid-email':
        return 'Adresse email invalide';
      case 'auth/too-many-requests':
        return 'Trop de tentatives. Veuillez réessayer plus tard';
      case 'auth/network-request-failed':
        return 'Erreur de connexion. Vérifiez votre connexion internet';
      default:
        return 'Une erreur est survenue. Veuillez réessayer';
    }
  }
}

// Instance singleton
export const authService = new AuthService();
export default authService;
