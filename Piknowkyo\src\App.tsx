// App.tsx

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLang } from './LangProvider';
import { usePushNotifications } from './services/usePushNotifications';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { FiMenu } from 'react-icons/fi';
import { useAppDispatch } from './store/hooks';
import { fetchSessions } from './store/slices/sessionsSlice';
import { fetchJournalEntries } from './store/slices/journalSlice';
import { fetchAudioAssets } from './store/slices/audioAssetsSlice';

import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import SessionsPage from './pages/SessionsPage';
import SessionDetailPage from './pages/SessionDetailPage';
import PlayerPage from './pages/PlayerPage';
import JournalPage from './pages/JournalPage';
import StatsPage from './pages/StatsPage';
import LeaderboardPage from './pages/LeaderboardPage';
import BlogPage from './pages/BlogPage';
import BlogPostCommentsPage from './pages/BlogPostCommentsPage'; // Assurez-vous d'avoir ce composant
import GamesPage from './pages/GamesPage';
import MonetizationPage from './pages/MonetizationPage';
import CategoriesPage from './pages/CategoriesPage';
import HistoryPage from './pages/HistoryPage';
import SettingsPage from './pages/SettingsPage';
import AudioAssetsConfigPage from './pages/AudioAssetsConfigPage'; // <<< NOUVEL IMPORT

import NotFoundPage from './pages/NotFoundPage';
import ProfilePage from './pages/ProfilePage';
// import QuizPage from './pages/QuizPage'; // Décommentez si utilisé
import ReduxExample from './components/ReduxExample';

import BottomBar from './components/BottomBar';
import NetworkStatusNotifier from './components/NetworkStatusNotifier';
import SplashScreen from './components/SplashScreen';
import MainMenu from './components/MainMenu';
import SyncStatusIndicator from './components/SyncStatusIndicator';
import { useTheme } from './ThemeProvider';
import TestFirebase from './testJoff/testFirebase';


const App: React.FC = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const { lang } = useLang();
  const { i18n, t } = useTranslation();
  const { darkMode, toggleTheme } = useTheme();
  const dispatch = useAppDispatch();
  usePushNotifications();

  useEffect(() => {
    if (i18n.language !== lang) {
      i18n.changeLanguage(lang);
    }
  }, [lang, i18n]);

  useEffect(() => {
    const timer = setTimeout(() => setShowSplash(false), 1200);
    return () => clearTimeout(timer);
  }, []);

  // Charger les données initiales
  useEffect(() => {
    dispatch(fetchSessions() as any);
    dispatch(fetchJournalEntries() as any);
    dispatch(fetchAudioAssets() as any);
  }, [dispatch]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const closeMenu = () => {
    setMenuOpen(false);
  };

  if (showSplash) {
    return <SplashScreen />;
  }

  return (
    <Router>
      <div className={`app-container ${darkMode ? 'dark-theme' : 'light-theme'}`}>
        <header className="app-header">
          <div className="logo">
            <img src="/logo192.png" alt="Piknowkyo Logo" style={{ height: 40, marginRight: 12 }} />
            <span>{t('app.name')}</span>
          </div>
          <div className="header-actions">
            <SyncStatusIndicator />
            <button
              className="theme-toggle"
              onClick={toggleTheme}
              aria-label={darkMode ? "Passer au thème clair" : "Passer au thème sombre"}
            >
              {darkMode ? '☀️' : '🌙'}
            </button>
            <button
              className="menu-toggle"
              onClick={toggleMenu}
              aria-label="Ouvrir le menu"
            >
              <FiMenu size={24} />
            </button>
          </div>
        </header>

        <MainMenu isOpen={menuOpen} onClose={closeMenu} />

        <main className="app-main">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/sessions" element={<SessionsPage />} />
            <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
            <Route path="/player/:sessionId" element={<PlayerPage />} />
            <Route path="/journal" element={<JournalPage />} />
            <Route path="/stats" element={<StatsPage />} />
            <Route path="/leaderboard" element={<LeaderboardPage />} />
            <Route path="/blog" element={<BlogPage />} />
            <Route path="/blog/:postId/comments" element={<BlogPostCommentsPage />} /> {/* Route pour les commentaires de blog */}
            <Route path="/games" element={<GamesPage />} />
            <Route path="/monetization" element={<MonetizationPage />} />
            <Route path="/categories" element={<CategoriesPage />} />
            <Route path="/history" element={<HistoryPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/settings/audio-assets" element={<AudioAssetsConfigPage />} /> {/* <<< NOUVELLE ROUTE ICI */}
            <Route path="/redux-example" element={<ReduxExample />} /> {/* <<< EXEMPLE REDUX */}
            {/* <Route path="/quiz" element={<QuizPage />} /> */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </main>

        <NetworkStatusNotifier />
        <BottomBar />
      </div>
    </Router>
  );
};

export default App;