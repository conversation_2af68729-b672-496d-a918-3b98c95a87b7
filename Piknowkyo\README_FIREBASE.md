# 🔥 Guide d'utilisation Firebase - PiKnowKyo

## 🚀 Démarrage rapide

### 1. Configuration initiale

1. **<PERSON><PERSON><PERSON> le fichier d'environnement** :
   ```bash
   cp .env.example .env
   ```

2. **Les credentials Firebase sont déjà configurés** dans le fichier `.env` pour le projet `piknowkyo-777`

3. **Installez les dépendances** :
   ```bash
   npm install
   ```

### 2. Initialisation de l'administration

Pour créer le premier utilisateur administrateur :

```bash
node scripts/init-admin.js
```

**Identifiants par défaut** :
- Email : `<EMAIL>`
- Mot de passe : `AdminPiknowkyo2024!`

### 3. Déploiement des règles de sécurité

```bash
# Rendre le script exécutable (Linux/Mac)
chmod +x scripts/deploy-firestore-rules.sh

# Exécuter le déploiement
./scripts/deploy-firestore-rules.sh
```

Ou manuellement :
```bash
firebase deploy --only firestore:rules
```

## 🔐 Authentification

### Types d'authentification supportés

1. **Email/Mot de passe** : Inscription et connexion classique
2. **Google OAuth** : Connexion avec compte Google
3. **Connexion anonyme** : Pour les utilisateurs invités

### Utilisation dans les composants

```typescript
import { useAuth } from '../contexts/AuthContext';

function MonComposant() {
  const { 
    currentUser, 
    userProfile, 
    signIn, 
    signUp, 
    signOut,
    isAuthenticated 
  } = useAuth();

  // Votre logique ici
}
```

## 📊 Structure des données

### Collections Firestore

- **`users/{userId}`** : Profils utilisateur
- **`user_sessions/{sessionId}`** : Sessions de l'utilisateur
- **`journal_entries/{entryId}`** : Entrées de journal
- **`audio_assets/{assetId}`** : Fichiers audio personnalisés
- **`scripts/{scriptId}`** : Scripts publics
- **`admin_users/{userId}`** : Utilisateurs administrateurs

### Exemple de profil utilisateur

```typescript
{
  uid: "user123",
  email: "<EMAIL>",
  displayName: "John Doe",
  preferences: {
    language: "fr",
    theme: "dark",
    notifications: true,
    offlineMode: true
  },
  subscription: {
    plan: "premium",
    status: "active",
    expiresAt: "2024-12-31"
  },
  stats: {
    totalSessions: 42,
    totalDuration: 1800,
    streakDays: 7
  }
}
```

## 📁 Stockage de fichiers

### Upload d'un fichier audio

```typescript
import storageService from '../services/storageService';

const uploadFile = async (file: File) => {
  const result = await storageService.uploadAudioFile(
    file, 
    'musics', // ou 'ambiants'
    (progress) => {
      console.log(`Upload: ${progress.percentage}%`);
    }
  );

  if (result.success) {
    console.log('Fichier uploadé:', result.audioAsset);
  }
};
```

### Récupération des fichiers utilisateur

```typescript
import { fetchUserAudioAssets } from '../data/audioAssets';

const loadUserFiles = async () => {
  try {
    const musics = await fetchUserAudioAssets('musics');
    const ambiants = await fetchUserAudioAssets('ambiants');
    
    console.log('Musiques:', musics);
    console.log('Ambiances:', ambiants);
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

## 🔄 Synchronisation offline/online

### Utilisation des services de sync

```typescript
import { SessionSyncService } from '../services/syncService';

// Synchroniser les sessions
const syncSessions = async () => {
  const pendingChanges = {
    created: [/* nouvelles sessions */],
    updated: [/* sessions modifiées */],
    deleted: [/* IDs des sessions supprimées */]
  };

  const result = await SessionSyncService.syncToFirebase(pendingChanges);
  
  if (result.success) {
    console.log(`${result.syncedCount} éléments synchronisés`);
  } else {
    console.error('Erreurs:', result.errors);
  }
};
```

## 🛡️ Sécurité

### Règles de sécurité principales

- **Authentification requise** : Tous les accès nécessitent une authentification
- **Isolation des données** : Chaque utilisateur ne peut accéder qu'à ses propres données
- **Permissions admin** : Les administrateurs ont des permissions spécifiques
- **Contenu public** : Scripts et modèles accessibles en lecture seule

### Vérification des permissions

```typescript
// Côté client - vérification basique
if (!auth.currentUser) {
  throw new Error('Utilisateur non connecté');
}

// Les règles Firestore font la vérification côté serveur
```

## 🔧 Développement

### Émulateurs Firebase

Pour utiliser les émulateurs en développement :

1. **Installer Firebase CLI** :
   ```bash
   npm install -g firebase-tools
   ```

2. **Configurer les émulateurs** :
   ```bash
   firebase init emulators
   ```

3. **Démarrer les émulateurs** :
   ```bash
   firebase emulators:start
   ```

4. **Activer les émulateurs dans l'app** :
   ```env
   REACT_APP_USE_FIREBASE_EMULATOR=true
   ```

### Variables d'environnement

```env
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_domain
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_bucket
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id

# Development
REACT_APP_USE_FIREBASE_EMULATOR=false
NODE_ENV=development
```

## 🚨 Dépannage

### Problèmes courants

1. **Erreur de permissions** :
   - Vérifiez que les règles Firestore sont déployées
   - Confirmez que l'utilisateur est bien authentifié

2. **Problème d'upload** :
   - Vérifiez la taille du fichier (max 50MB)
   - Confirmez le format supporté (MP3, WAV, OGG, M4A, AAC)

3. **Synchronisation échouée** :
   - Vérifiez la connexion internet
   - Consultez les logs dans la console Firebase

### Logs et monitoring

- **Console Firebase** : https://console.firebase.google.com/project/piknowkyo-777
- **Logs d'authentification** : Firebase Auth
- **Logs Firestore** : Firebase Firestore
- **Logs Storage** : Firebase Storage

## 📞 Support

Pour toute question technique :
1. Consultez la documentation Firebase
2. Vérifiez les logs dans la console
3. Testez avec les émulateurs en développement

---

**Note** : Cette intégration suit les meilleures pratiques de sécurité Firebase et respecte le RGPD.
