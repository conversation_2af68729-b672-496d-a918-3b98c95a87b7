import React from 'react';

const TestApp: React.FC = () => {
  return (
    <div style={{ 
      padding: '2rem', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1>🧪 Test PiKnowKyo</h1>
      
      <div style={{ 
        background: '#f0f0f0', 
        padding: '1rem', 
        borderRadius: '8px',
        marginBottom: '1rem'
      }}>
        <h2>✅ React fonctionne</h2>
        <p>Si vous voyez ce message, React est correctement configuré.</p>
      </div>

      <div style={{ 
        background: '#e8f5e8', 
        padding: '1rem', 
        borderRadius: '8px',
        marginBottom: '1rem'
      }}>
        <h2>🔧 Étapes suivantes</h2>
        <ol>
          <li>Vérifiez la console du navigateur (F12)</li>
          <li>Désactivez les bloqueurs de publicité</li>
          <li>Testez en mode incognito</li>
          <li><PERSON><PERSON>marrez le serveur de développement</li>
        </ol>
      </div>

      <div style={{ 
        background: '#fff3cd', 
        padding: '1rem', 
        borderRadius: '8px',
        border: '1px solid #ffeaa7'
      }}>
        <h2>⚠️ Problème courant</h2>
        <p>
          L'erreur <code>net::ERR_BLOCKED_BY_CLIENT</code> est généralement causée par :
        </p>
        <ul>
          <li>Bloqueurs de publicité (uBlock Origin, AdBlock)</li>
          <li>Extensions de sécurité</li>
          <li>Antivirus avec protection web</li>
          <li>Pare-feu d'entreprise</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '2rem',
        padding: '1rem',
        background: '#e3f2fd',
        borderRadius: '8px'
      }}>
        <h2>🔗 Liens utiles</h2>
        <ul>
          <li><a href="/firebase-test">Test Firebase</a></li>
          <li><a href="/settings">Paramètres</a></li>
          <li><a href="/about">À propos</a></li>
        </ul>
      </div>
    </div>
  );
};

export default TestApp;
