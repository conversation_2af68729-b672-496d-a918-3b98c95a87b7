// src/data/audioAssets.ts
import { AudioAsset, AudioManifest } from '../models';

// src/data/audioAssets.ts (ou où votre fonction est définie)
// import { storage, db } from '../services/firebase'; // Vos instances Firebase
// import { ref, deleteObject } from "firebase/storage";
// import { doc, deleteDoc } from "firebase/firestore";

export interface DefaultAudioAsset {
    id: string;
    name: string;
    url: string; // Chemin relatif dans /public/assets/
    type: 'music' | 'ambient';
  }
  
  // Fonction pour charger les assets par défaut depuis le manifest JSON
  export const fetchAudioAssets = async (): Promise<AudioManifest> => {
    try {
      const response = await fetch('/assets/audio_manifests/audio_manifest.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      // Transformer les URL relatives en URL absolues si nécessaire pour la lecture
      const transformPaths = (assets: DefaultAudioAsset[]): DefaultAudioAsset[] => {
          return assets.map(asset => ({
              ...asset,
              url: asset.url.startsWith('/') ? asset.url : `/assets/audio/${asset.type === 'music' ? 'musics' : 'ambiants'}/${asset.url}`
          }));
      };
      return {
          musics: transformPaths(data.musics || []),
          ambiants: transformPaths(data.ambiants || []),
      };
    } catch (error) {
      console.error("Could not fetch audio manifest:", error);
      return { musics: [], ambiants: [] }; // Retourner des listes vides en cas d'erreur
    }
  };
import storageService, { UploadProgress } from '../services/storageService';
import { auth } from '../firebase';

// Upload d'un asset audio avec Firebase
export const uploadAudioAsset = async (
  file: File,
  type: 'musics' | 'ambiants',
  onProgress?: (progress: UploadProgress) => void
): Promise<AudioAsset> => {
  if (!auth.currentUser) {
    throw new Error('Utilisateur non connecté');
  }

  const result = await storageService.uploadAudioFile(file, type, onProgress);

  if (!result.success || !result.audioAsset) {
    throw new Error(result.error || 'Erreur lors de l\'upload');
  }

  return result.audioAsset;
};
  
// Suppression d'un asset audio avec Firebase
export const deleteUserAudioAsset = async (audioAsset: AudioAsset): Promise<void> => {
  if (!auth.currentUser) {
    throw new Error('Utilisateur non connecté');
  }

  const success = await storageService.deleteAudioFile(audioAsset);

  if (!success) {
    throw new Error('Erreur lors de la suppression');
  }
};
  
// Récupérer les assets audio d'un utilisateur depuis Firebase
export const fetchUserAudioAssets = async (type?: 'musics' | 'ambiants'): Promise<AudioAsset[]> => {
  if (!auth.currentUser) {
    throw new Error('Utilisateur non connecté');
  }

  try {
    const { AudioAssetSyncService } = await import('../services/syncService');
    const allAssets = await AudioAssetSyncService.fetchFromFirebase();

    if (type) {
      const filterType = type === 'musics' ? 'music' : 'ambient';
      return allAssets.filter(asset => asset.type === filterType);
    }

    return allAssets;
  } catch (error: any) {
    console.error('Erreur lors de la récupération des assets:', error);
    throw new Error('Impossible de récupérer les fichiers audio');
  }
};
  