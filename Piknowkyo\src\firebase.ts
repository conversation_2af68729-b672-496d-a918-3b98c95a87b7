import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getAnalytics } from 'firebase/analytics';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Configuration Firebase sécurisée
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "piknowkyo-777.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "piknowkyo-777",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "piknowkyo-777.firebasestorage.app",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "375619599814",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID || "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Initialiser les services Firebase
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Analytics (optionnel, seulement en production)
let analytics: any = null;
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  analytics = getAnalytics(app);
}
export { analytics };

// Connecter aux émulateurs en développement
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectAuthEmulator(auth, 'http://localhost:9099');
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('🔧 Connected to Firebase emulators');
  } catch (error) {
    console.warn('⚠️ Firebase emulators already connected or not available');
  }
}

// La persistance est maintenant gérée par la nouvelle API localCache ci-dessus
